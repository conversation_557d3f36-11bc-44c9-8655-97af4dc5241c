import { authService } from '@/services/authService'

export async function POST(request: Request) {
  const body = await request.json()
  const email = body.email as string
  const password = body.password as string
  const baseUrl = process.env.USER_ENPOINT as string

  const { token } = await authService.loginFromServer(email, password, baseUrl)
  const expiresDate = getTokenExpiresDate(token)
  return Response.json(
    {
      message: 'Login successfully',
    },
    {
      status: 200,
      headers: {
        'Set-Cookie': `sessionToken=${token}; Path=/; HttpOnly; Expires=${expiresDate}; SameSite=Lax; Secure`,
      },
    },
  )
}
function getTokenExpiresDate(token: string) {
  const tokenParts = token.split('.')
  const payload = JSON.parse(atob(tokenParts[1]))
  return new Date(payload.exp * 1000)
}
