'use client'
import { createContext, useState } from 'react'

interface AuthContextType {
  user: string
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({
  children,
  initialUser,
  initialIsAuthenticated,
}: { children: React.ReactNode; initialUser: string; initialIsAuthenticated: boolean }) {
  const [user, setUser] = useState<string>(initialUser)
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(initialIsAuthenticated)

  return <AuthContext.Provider value={{ user, isAuthenticated }}>{children}</AuthContext.Provider>
}

export default AuthContext
