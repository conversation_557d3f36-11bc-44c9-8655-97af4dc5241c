import { authService } from '@/services/authService'
// biome-ignore lint/style/useImportType: <explanation>
import { NextRequest, NextResponse } from 'next/server'

export interface AuthContext {
  protect: () => Promise<NextResponse | undefined>
  user?: any | null
  isAuthenticated: boolean
}

export type MiddlewareHandler = (
  auth: AuthContext,
  req: NextRequest,
) => Promise<void> | void | NextResponse

export function customMiddleware(handler: MiddlewareHandler, options?: { signInUrl?: string }) {
  return async (req: NextRequest) => {
    let isAuthenticated = false
    let user = null
    const token = req.cookies.get('sessionToken')?.value
    if (token) {
      const baseUrl = process.env.USER_ENDPOINT as string
      const validation = await authService.validateToken(token, baseUrl)
      if (validation.valid) {
        isAuthenticated = true
        user = 'some user'
      }
    }
    const auth: AuthContext = {
      isAuthenticated,
      user,
      protect: async () => {
        if (!isAuthenticated) {
          return NextResponse.redirect(new URL(options?.signInUrl ?? '/sign-in', req.url))
        }
      },
    }

    // run your handler
    const result: any = await handler(auth, req)
    if (result instanceof NextResponse) return result

    return NextResponse.next({
      request: {
        headers: new Headers({
          ...Object.fromEntries(req.headers),
          'x-authenticated': String(auth.isAuthenticated),
          'x-user': auth.user ? JSON.stringify(auth.user) : '',
        }),
      },
    })
  }
}
